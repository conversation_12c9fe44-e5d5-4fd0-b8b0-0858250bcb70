import { nativeFetch } from '@/shared/lib';
import { CustomFormData } from '@/shared/model';

interface SignUpCredentials {
  email: string;
  password: string;
}

interface SignUpProfile {
  firstName: string;
  lastName: string;
  imageRef?: string;
}

interface SignUpData extends SignUpCredentials, SignUpProfile {}

interface OtpRequest {
  type:
    | 'email-verification'
    | 'sign-in'
    | 'forget-password'
    | 'update-password';
  userId: string;
}

interface OtpVerifyRequest {
  userId: string;
  otp: string;
  type: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isEmailVerified: boolean;
  imageRef?: string;
  createdAt: string;
  updatedAt: string;
}

export type SignUpResponse = {
  message: string;
  user: User;
};

export type OtpResponse = {
  message: string;
};

export type OtpVerifyResponse = {
  message: string;
  user: User;
};

// Check if email is already used
export async function checkEmailExists(email: string): Promise<boolean> {
  try {
    const response = await nativeFetch(`/api/users?email=${email}`, {
      method: 'GET',
      credentials: 'include',
    });

    return response.ok;
  } catch (error) {
    return false;
  }
}

// Check if user has credentials (password)
export async function checkUserHasCredentials(email: string): Promise<boolean> {
  try {
    // First, get the user ID from email
    const userResponse = await nativeFetch(`/api/users?email=${email}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!userResponse.ok) {
      return false;
    }

    const userData = await userResponse.json();
    const userId = userData.user?.id;

    if (!userId) {
      return false;
    }

    // Then check if the user has credentials
    const credentialsResponse = await nativeFetch(
      `/api/users/${userId}/credentials`,
      {
        method: 'GET',
        credentials: 'include',
      }
    );

    return credentialsResponse.ok;
  } catch (error) {
    console.error('Error checking credentials:', error);
    return false;
  }
}

// Sign up with email and password
export async function signUp(data: SignUpData): Promise<SignUpResponse> {
  const response = await nativeFetch('/api/auth/sign-up/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Sign-up failed');
  }

  return response.json();
}

// Send OTP for email verification
export async function sendOtp(data: OtpRequest): Promise<OtpResponse> {
  const response = await nativeFetch('/api/auth/otp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to send OTP');
  }

  return response.json();
}

// Verify OTP
export async function verifyOtp(
  data: OtpVerifyRequest
): Promise<OtpVerifyResponse> {
  const response = await nativeFetch('/api/auth/otp/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('OTP verification failed');
  }

  return response.json();
}

// Upload media and get reference
export async function uploadMedia(
  fileUri: string
): Promise<{ imageRef: string }> {
  try {
    // Create FormData object
    const formData = new CustomFormData();

    // Extract filename from URI
    const filename = fileUri.split('/').pop() || 'image.jpg';
    // Get file extension
    const match = /\.(\w+)$/.exec(filename);
    const type = match ? `image/${match[1]}` : 'image/jpeg';

    console.log({
      uri: fileUri,
      name: filename,
      type,
    });

    // Add file to FormData
    // @ts-expect-error - FormData in React Native has slightly different implementation
    formData.append('file', {
      uri: fileUri,
      name: filename,
      type,
    });

    const response = await nativeFetch('/api/files', {
      method: 'POST',
      body: formData,
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to upload media');
    }

    return response.json();
  } catch (error) {
    console.error('Error uploading media:', error);
    throw error;
  }
}
