import * as secureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

import type { <PERSON><PERSON> } from 'set-cookie-parser';

import { catchError, type CatchErrorResult } from './catch-error';

type StorageOptions = {
  secure?: boolean;
};

export async function store(
  name: string,
  value: string,
  options?: StorageOptions
): Promise<CatchErrorResult<void>> {
  if (options?.secure) {
    return catchError(secureStore.setItemAsync(name, value));
  } else {
    return catchError(AsyncStorage.setItem(name, value));
  }
}

export async function getStoredItem(
  name: string,
  options?: StorageOptions
): Promise<CatchErrorResult<string>> {
  let result: CatchErrorResult<string | null>;
  if (options?.secure) {
    result = await catchError(secureStore.getItemAsync(name));
  } else {
    result = await catchError(AsyncStorage.getItem(name));
  }

  if (!result.success) {
    return result;
  }

  const { data: item } = result;

  if (item === null) {
    return {
      success: false,
      error: new Error(`Item with name ${name} not found`),
    };
  }

  return result as CatchErrorResult<string>;
}

export async function deleteStoredItem(
  name: string,
  options?: StorageOptions
): Promise<CatchErrorResult<void>> {
  if (options) {
    if (options.secure) {
      return catchError(secureStore.deleteItemAsync(name));
    } else {
      return await catchError(AsyncStorage.removeItem(name));
    }
  }

  try {
    Promise.all([
      secureStore.deleteItemAsync(name),
      AsyncStorage.removeItem(name),
    ]);

    return { success: true, data: undefined };
  } catch (error) {
    return {
      success: false,
      error: new Error(`Could not delete item with name ${name}`),
    };
  }
}

export async function storeCookie(cookie: Cookie) {
  // If the cookie has no value, or is expired, we delete it...
  const isEmpty = cookie.value === '';
  const hasInvalidMaxAge = cookie.maxAge !== undefined && cookie.maxAge <= 0;
  const isExpired =
    cookie.expires !== undefined && cookie.expires.getTime() < Date.now();

  if (isEmpty || hasInvalidMaxAge || isExpired) {
    return await deleteStoredItem(cookie.name);
  }

  // ... Otherwise, we store it
  const storeSecurely = cookie.httpOnly || cookie.secure || false;
  return await store(cookie.name, cookie.value, { secure: storeSecurely });
}
