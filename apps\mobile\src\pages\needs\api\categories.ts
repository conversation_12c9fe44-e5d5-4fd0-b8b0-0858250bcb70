import { nativeFetch } from '@/shared/lib';

export type Category = {
  id: string;
  name: string;
};

export async function getCategories() {
  const response = await nativeFetch('/api/categories', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Could not fetch categories');
  }

  const data = await response.json();
  return data.categories as Category[];
}
