import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';

import { fileTypeFromBuffer } from 'file-type';
import status from 'http-status';

import { logger } from '@/shared/lib';
import { requireAuthMiddleware } from '@/shared/middlewares';
import type { AuthVariables } from '@/shared/model';

import { fileRepository } from './file.repository';
import { uploadFileDto } from './file.dto';
import { fileService } from './file.service';

// 5 mb
const MAX_FILE_SIZE = 5 * 1024 * 1024;

export const fileRoutes = new Hono<{
  Variables: AuthVariables;
}>();

// Upload media file - No auth middleware here
fileRoutes.post('/', zValidator('form', uploadFileDto), async (c) => {
  const body = c.req.valid('form');

  let file = body.file;

  // limit file size
  if (file.size > MAX_FILE_SIZE) {
    throw new HTTPException(status.BAD_REQUEST, {
      res: Response.json({
        error: {
          code: status[`${status.BAD_REQUEST}_NAME`],
          message: 'File size is too large',
        },
      }),
    });
  }

  let fileType = file.type;
  if (fileType === 'other') {
    logger.info('Could not detect file type, trying to detect it');

    // Convert the file in ArrayBuffer and then Buffer to be used by file-type library
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // If the type has been successfully detected, update the file type
    const detectedType = await fileTypeFromBuffer(buffer);
    if (detectedType) {
      fileType = detectedType.mime;

      file = new File([buffer], file.name, {
        type: fileType,
        lastModified: file.lastModified,
      });
    }
  }

  // // const buffer = Buffer.from(await file.arrayBuffer());
  // const fileName = file.name;
  // // const contentType = file.type;
  // // // Generate temporary imageRef using UUID
  // // const tempImageRef = `temp/${uuidv4()}_${fileName}`;

  // const result = await fileRepository
  //   .uploadFile
  //   // '', // Pass empty string for userId, it will be replaced in the repository
  //   // buffer,
  //   // tempImageRef, // Use temporary imageRef
  //   // contentType
  //   ();

  // return c.json({
  //   imageRef: result.imageRef, // Return temporary imageRef
  //   url: result.url,
  // });

  const result = await fileService.uploadFile(file);

  return c.json({
    file: result,
  });
});

// Apply auth middleware to GET and DELETE routes
fileRoutes.use('*', requireAuthMiddleware);

// Get media URL
fileRoutes.get('/:imageRef', async (c) => {
  try {
    const imageRef = c.req.param('imageRef');
    const url = await fileRepository.getMediaUrl(imageRef);
    return c.json({ url });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message:
            error instanceof Error ? error.message : 'Failed to get media URL',
        },
      }),
    });
  }
});

// Delete media
fileRoutes.delete('/:imageRef', async (c) => {
  try {
    const session = c.get('session')!;
    const imageRef = c.req.param('imageRef');

    // Security check: ensure the user can only delete their own files
    if (!imageRef.startsWith(session.user.id + '/')) {
      throw new HTTPException(status.FORBIDDEN, {
        res: Response.json({
          error: {
            code: status[`${status.FORBIDDEN}_NAME`],
            message: 'You do not have permission to delete this file',
          },
        }),
      });
    }

    await fileRepository.deleteMedia(imageRef);
    return c.json({ success: true });
  } catch (error) {
    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message:
            error instanceof Error ? error.message : 'Failed to delete media',
        },
      }),
    });
  }
});
