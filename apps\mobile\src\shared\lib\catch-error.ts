export type CatchErrorResult<T, E = Error> =
  | {
      success: true;
      data: T;
    }
  | {
      success: false;
      error: E;
    };

export async function catchError<T>(
  promise: Promise<T>
): Promise<CatchErrorResult<T, Error>> {
  return promise
    .then((data): CatchE<PERSON>rResult<T, Error> => ({ success: true, data }))
    .catch((error): CatchErrorResult<T, Error> => ({ success: false, error }));
}

export async function catchTypedError<T, E extends new (...args: any) => Error>(
  promise: Promise<T>,
  errorsToCatch?: E[]
): Promise<CatchErrorResult<T, InstanceType<E>>> {
  return promise
    .then(
      (data): CatchErrorResult<T, InstanceType<E>> => ({ success: true, data })
    )
    .catch((error): CatchErrorResult<T, InstanceType<E>> => {
      if (!errorsToCatch) {
        // display error type in console
        console.error(error);
        return { success: false, error };
      }

      if (errorsToCatch.some((errorType) => error instanceof errorType)) {
        return { success: false, error };
      }

      throw error;
    });
}
