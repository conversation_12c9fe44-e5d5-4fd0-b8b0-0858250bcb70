import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

import { deleteStoredItem, getStoredItem, store } from '@/shared/lib';

import type { CredentialsSchema, ProfileSchema } from './sign-up-schema';

interface SignUpState {
  // Form data
  credentials: Partial<CredentialsSchema>;
  profile: Partial<ProfileSchema>;
  isSocialAccount: boolean;

  // Functions
  setCredentials: (data: Partial<CredentialsSchema>) => void;
  setProfile: (data: Partial<ProfileSchema>) => void;
  setSocialAccount: (value: boolean) => void;
  reset: () => void;
}

// Create a storage object that works with both web and mobile
const storage = {
  getItem: async (name: string) => {
    try {
      return await getStoredItem(name).then((result) =>
        result.success ? result.data : null
      );
    } catch {
      return null;
    }
  },
  setItem: async (name: string, value: string) => {
    try {
      await store(name, value);
    } catch {
      // Handle errors
    }
  },
  removeItem: async (name: string) => {
    try {
      await deleteStoredItem(name);
    } catch {
      // Handle errors
    }
  },
};

export const useSignUpStore = create<SignUpState>()(
  persist(
    (set) => ({
      // Initial state
      credentials: {
        email: '',
        password: '',
        confirmPassword: '',
      },
      profile: {
        imageUri: '',
        firstName: '',
        lastName: '',
      },
      isSocialAccount: false,

      // Actions
      setCredentials: (data) =>
        set((state) => ({
          credentials: { ...state.credentials, ...data },
        })),

      setProfile: (data) =>
        set((state) => ({
          profile: { ...state.profile, ...data },
        })),

      setSocialAccount: (value) =>
        set(() => ({
          isSocialAccount: value,
        })),

      reset: () =>
        set({
          credentials: {
            email: '',
            password: '',
            confirmPassword: '',
          },
          profile: {
            imageUri: '',
            firstName: '',
            lastName: '',
          },
          isSocialAccount: false,
        }),
    }),
    {
      name: 'sign-up-storage',
      storage: createJSONStorage(() => storage),
    }
  )
);
