import { Platform } from 'react-native';

import setCookie, { splitCookiesString } from 'set-cookie-parser';

import { backendBaseUrl, sessionCookieName } from '@/shared/config';
import { getStoredItem, storeCookie } from '@/shared/lib';

// Get the parameters of the default fetch function
type NativeFetchParams = Parameters<typeof fetch>;
type FetchSource = 'backend-api' | 'external';

export async function nativeFetch(
  input: NativeFetchParams[0],
  init: NativeFetchParams[1] & {
    source?: FetchSource;
  }
) {
  // If no source is specified, we assume it's a backend API request
  if (init.source === undefined) {
    init.source = 'backend-api';
  }

  // If the source is a backend API request, we prepend the backend base URL
  if (init.source === 'backend-api') {
    input = backendBaseUrl + input;
  }

  if (Platform.OS !== 'web' && init.credentials === 'include') {
    const sessionToken = await getStoredItem(sessionCookieName, {
      secure: true,
    }).then((result) => (result.success ? result.data : null));

    if (sessionToken) {
      init.headers = {
        ...init.headers,
        Cookie: `connect.sid=${sessionToken}`,
      };
    }
  }

  const response = await fetch(input, init);

  // If the response is a backend API response, we check for cookies and store them
  if (Platform.OS !== 'web' && init?.credentials === 'include') {
    // Get all the cookies from the response as a single string
    const setCookieHeader = response.headers.get('set-cookie');

    if (setCookieHeader) {
      // Split each cookie string
      const cookieStrings = splitCookiesString(setCookieHeader);

      // Parse each cookie string into a cookie object and store it
      cookieStrings.forEach(async (cookieString) => {
        const cookie = setCookie(cookieString)[0];

        await storeCookie(cookie);
      });
    }
  }

  return response;
}
